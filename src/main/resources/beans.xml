<beans xmlns="http://www.springframework.org/schema/beans"
       xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
       xmlns:c="http://www.springframework.org/schema/c" xmlns:p="http://www.springframework.org/schema/p"
       xmlns:context="http://www.springframework.org/schema/context"
       xsi:schemaLocation="http://www.springframework.org/schema/beans
						http://www.springframework.org/schema/beans/spring-beans.xsd http://www.springframework.org/schema/context https://www.springframework.org/schema/context/spring-context.xsd">
    <context:property-placeholder location="/application.properties"/>
<!--    https://flutteruki.atlassian.net/wiki/spaces/SportsbookPlatform/pages/97390120/Market+Stream+Client+NG+-+Step-by-Step+integration+Guide-->
<!--    <import resource="classpath:conf/fms-client-application.xml"/>-->
    <import resource="classpath:conf/fms-new-client-application.xml"/>
    <!--Work arrounds-->
    <bean id="statsMBeanExporter" class="org.springframework.jmx.export.MBeanExporter"/>
    <bean id="httpConnPoolExporter" class="org.springframework.jmx.export.MBeanExporter" lazy-init="true"/>
    <bean name="fms.client.marketView.metrics.service"
          class="com.flutter.marketstreamcomparisontool.mock.NoMetrics"/>
    <bean id="msct.noMetrics"
          class="com.flutter.marketstreamcomparisontool.mock.NoMetrics"/>

<!--    My config for market stream client-->
<!--    <bean id="msct.marketStreamCacheInitializer"-->
<!--          class="com.flutter.marketstreamcomparisontool.client.MarketStreamInitializer"-->
<!--          c:ms-ref="fms.client"-->

<!--    />-->


</beans>