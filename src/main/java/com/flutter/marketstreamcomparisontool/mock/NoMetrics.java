package com.flutter.marketstreamcomparisontool.mock;

import com.ppb.platform.sb.fmg.metrics.service.MarketViewMetricsService;
import org.quartz.SchedulerException;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Component;

@Component
public class NoMetrics implements MarketViewMetricsService {
    private static final Logger LOGGER = LoggerFactory.getLogger(NoMetrics.class);
    @Override
    public void start() throws SchedulerException {
        LOGGER.info("operation=Start, msg='No metrics Impl'");
    }

    @Override
    public void stop() throws SchedulerException {
        LOGGER.info("operation=Stop, msg='No metrics Impl'");
    }
}
