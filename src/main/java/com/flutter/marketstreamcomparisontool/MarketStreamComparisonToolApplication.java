package com.flutter.marketstreamcomparisontool;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.boot.SpringApplication;
import org.springframework.boot.autoconfigure.SpringBootApplication;
import org.springframework.context.annotation.ImportResource;

@ImportResource("classpath:beans.xml")
@SpringBootApplication
public class MarketStreamComparisonToolApplication {
    private static final Logger LOGGER = LoggerFactory.getLogger(MarketStreamComparisonToolApplication.class);
    public static void main(String[] args) {
        LOGGER.info("operation=Start, msg='Spring Boot - {}'", args);
        SpringApplication.run(MarketStreamComparisonToolApplication.class, args);
    }


}
