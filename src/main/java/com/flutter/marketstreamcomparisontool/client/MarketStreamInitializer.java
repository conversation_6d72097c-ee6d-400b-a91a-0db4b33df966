package com.flutter.marketstreamcomparisontool.client;

import com.betfair.platform.fms.cache.KafkaMarketViewCache;
import com.betfair.platform.fms.notification.MarketStreamClientNotificationService;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.stereotype.Component;


@Component
public class MarketStreamInitializer {
    private static final Logger LOGGER = LoggerFactory.getLogger(MarketStreamInitializer.class);


    public MarketStreamInitializer(
            @Qualifier("fms.client") KafkaMarketViewCache ms,
            MarketStreamClientNotificationService providedMarketChangeListener,
            CustomMarketChangeListener customMarketChangeListener
            ) {
        LOGGER.info("operation=Start, msg='Initializing MarketStream client'");
        providedMarketChangeListener.addMarketChangeListener(customMarketChangeListener);
        ms.start();
    }

}
